# 项目重构计划 - TODO

## 总体目标
将当前的 Next.js 项目完全重构为 Remix + Cloudflare 架构，采用现代化的 monorepo 分层架构，完整运行在 Cloudflare 生态系统中。

## 架构设计
```
repo-root/
├─ apps/                       # 可执行产物
│  ├─ web/                     # Remix Edge —— 主 Web UI (Cloudflare Pages)
│  ├─ worker/                  # Cloudflare Worker (R2 预签名，Webhook...)
│  └─ e2e/                     # Playwright 黑盒测试
└─ packages/                   # 可复用逻辑或 UI
   ├─ infra/                   # 最内层：无业务，仅基建
   ├─ features/                # 业务用例（纯函数/Service，不含 HTTP）
   └─ ui-kit/                  # Tailwind/Headless UI 组件
```

### 单向依赖守则
```
apps/* ➜ packages/features/* ➜ packages/infra/*
```
- **apps** 只 import features、ui-kit
- **features** 只 import infra、ui-kit，绝不互相跨 feature
- **infra** 对上层一无所知

### 工具链约束
- **Biome** 代码质量链 (替代 ESLint/Prettier)
- **Turborepo** 增量构建和远程缓存
- **Cloudflare Wrangler** 统一部署管理

---

## 阶段一：基础设施层 (packages/infra) ✅ 已完成

### 已完成项目
- [x] 环境变量管理 (config) - zod/env & runtime constants
- [x] 数据库集成 (db) - Drizzle ORM + Neon PostgreSQL
- [x] 认证系统 (auth) - BetterAuth singleton
- [x] 文件存储 (r2) - S3-compatible helper & signer
- [x] 邮件服务 (mailer) - Resend client
- [x] TypeScript 配置和构建流程

### 技术栈重点
- **Cloudflare 原生**: 完全适配 Cloudflare Workers 环境
- **Drizzle ORM**: 边缘计算友好的 ORM
- **BetterAuth**: JWT strategy + Google OAuth + Email OTP
- **R2 Storage**: S3 兼容的对象存储

---

## 阶段二：UI 组件库 (packages/ui-kit) ✅ 已完成

### 已完成项目
- [x] 基础组件 (Button, Input, Label, Badge, Card 系列)
- [x] Tailwind/Headless UI 组件体系
- [x] 响应式设计支持
- [x] 亮色/暗色主题切换
- [x] 无障碍支持 (基于 Radix UI)

### Cloudflare 优化
- [x] 零运行时 CSS 生成
- [x] 树摇优化支持
- [x] 边缘友好的组件设计

---

## 阶段三：BetterAuth 认证系统集成 🔄 进行中

### 技术选择 - Cloudflare 最佳实践
- **会话管理**: JWT + HTTP-only cookies (边缘友好，无需外部存储)
- **认证流程**: 全套支持 (email/password, Google OAuth, email OTP, 密码重置)
- **权限控制**: 基于角色的访问控制 (admin, user)
- **路由保护**: Remix loader 函数进行服务端验证

### 核心实现任务
- [ ] **基础设置**
  - [ ] 添加 @repo/auth 到 web 应用依赖
  - [ ] 创建 Remix 认证工具函数 (会话验证, 用户提取)
  - [ ] 配置环境变量和 Wrangler 设置

- [ ] **认证路由** (apps/web/app/routes/_auth.*)
  - [ ] 登录页面 (email/password + Google OAuth)
  - [ ] 注册页面 (带邮箱验证)
  - [ ] 登出处理
  - [ ] 密码重置流程 (请求 + 确认)
  - [ ] 邮箱验证路由

- [ ] **UI 组件** (使用 @repo/ui-kit)
  - [ ] 登录/注册表单
  - [ ] 用户资料/设置页面 (显示角色)
  - [ ] 认证状态组件 (登录/登出按钮, 用户菜单)

- [ ] **路由保护**
  - [ ] 受保护路由 loader 工具函数
  - [ ] 基于角色的访问控制
  - [ ] 数据库用户角色字段

- [ ] **系统集成**
  - [ ] 更新 root.tsx 认证上下文
  - [ ] 创建演示受保护路由 (仪表板, 管理面板)

### 待实现功能包 (后续阶段)
- [ ] **ai**: AI 工具集成
- [ ] **analytics**: 数据分析  
- [ ] **billing**: 计费系统
- [ ] **cms**: 内容管理

---

## 阶段四：应用层构建 (apps/) ⏳ 待开始

### 目标架构
```
apps/
├─ web/                        # Remix Edge App (Cloudflare Pages)
│  ├─ app/routes/              # 仅协议层：loader/action & HTTP glue
│  ├─ public/
│  ├─ wrangler.toml           # Pages Functions 配置
│  └─ package.json
├─ worker/                     # 独立 Cloudflare Worker
│  ├─ src/index.ts            # R2 预签名，Webhook 处理
│  ├─ wrangler.toml           # Worker 配置
│  └─ package.json
└─ e2e/                       # Playwright 测试
   ├─ tests/
   └─ package.json
```

### 迁移计划
- [ ] **创建 Remix Edge 应用**
  - [ ] 设置 Remix + Cloudflare Pages Functions
  - [ ] 配置 Cloudflare Workers 兼容模式
  - [ ] 集成 BetterAuth 中间件
- [ ] **创建独立 Worker**
  - [ ] R2 预签名 URL 服务
  - [ ] Webhook 处理器
  - [ ] 后台任务处理
- [ ] **路由迁移** (仅协议层)
  - [ ] 用户认证流程 (`_auth.*.tsx`)
  - [ ] 文件上传 (`uploads.*.tsx`)
  - [ ] 用户仪表板 (`dashboard.*.tsx`)
  - [ ] 设置页面 (`settings.*.tsx`)

### Cloudflare 特性利用
- **Pages Functions**: SSR + 边缘计算
- **Workers**: 独立微服务
- **R2**: 对象存储
- **D1**: 备选边缘数据库
- **KV**: 缓存和会话存储

---

## 阶段五：工具链集成 🔄 进行中

### Turborepo 配置
- [x] 基础 turbo.json 配置
- [ ] **构建流水线**
  - [ ] typecheck → lint → build → dev
  - [ ] 增量构建和远程缓存
  - [ ] 包依赖管理

### Biome 代码质量
- [ ] **替换 ESLint/Prettier**
  - [ ] 配置 biome.json
  - [ ] 设置 noRestrictedImports 规则
  - [ ] CI 集成 `pnpm biome ci .`

### Wrangler 部署配置
- [ ] **Pages Functions 配置**
  ```toml
  # apps/web/wrangler.toml
  name = "remix-web"
  pages_build_output_dir = "./public"
  compatibility_date = "2025-06-27"
  ```
- [ ] **独立 Worker 配置**
  ```toml
  # apps/worker/wrangler.toml
  name = "api-worker"
  main = "./src/index.ts"
  compatibility_date = "2025-06-27"
  
  [[r2_buckets]]
  binding = "R2"
  bucket_name = "app-assets"
  ```

---

## 阶段六：CI/CD 流程 ⏳ 待开始

### GitHub Actions 流程
- [ ] **PR 检查** (`ci/pr.yml`)
  - [ ] `pnpm biome ci .`
  - [ ] `pnpm turbo run typecheck`
  - [ ] `pnpm turbo run test`
- [ ] **部署流程** (`ci/deploy.yml`)
  - [ ] `pnpm turbo run build --filter=apps/web...`
  - [ ] `wrangler pages deploy ./apps/web/public`
  - [ ] `pnpm turbo run build --filter=apps/worker`
  - [ ] `wrangler deploy --config apps/worker/wrangler.toml`

### 环境管理
- [ ] **开发环境**: wrangler dev + 本地数据库
- [ ] **预览环境**: Cloudflare Preview Deployments
- [ ] **生产环境**: Cloudflare 生产部署

---

## 代码示例规范

### packages/infra 示例
```typescript
// packages/infra/config/env.ts
export const ENV = makeZodEnv({
  DATABASE_URL: z.string().url(),
  JWT_SECRET: z.string().min(32),
  R2_ACCESS_KEY: z.string(),
  RESEND_KEY: z.string()
});

// packages/infra/auth/index.ts
export const auth = betterAuth({
  database: drizzleAdapter(db),
  session: { strategy: "jwt", expiresIn: "30d" },
  providers: [googleProvider({...})],
  plugins: [emailOtpPlugin({...})]
});
```

### packages/features 示例
```typescript
// packages/features/auth/server.ts
export async function login(data: LoginInput) {
  const { cookie, user } = await auth.signInWithPassword(data);
  return { cookie, user };
}

// packages/features/auth/client.ts
export function useLogin() {
  // React hooks for client-side
}
```

### apps/web 示例
```typescript
// apps/web/app/routes/_auth.login.tsx
export const action = async ({ request }) => {
  const body = Object.fromEntries(await request.formData());
  const res = await login(body);
  return redirect("/", { 
    headers: { "Set-Cookie": res.cookie.serialize() } 
  });
};
```

---

## 开发工作流

### 本地开发
```bash
# 并行启动所有应用
pnpm turbo run dev --filter=apps/web... --filter=apps/worker...

# 单独开发特定应用
pnpm turbo run dev --filter=apps/web
```

### 构建部署
```bash
# 增量构建
pnpm turbo run build

# 部署到 Cloudflare
pnpm run deploy
```

---

## 当前状态总结

### 已完成 ✅
1. **基础设施层** - Cloudflare 原生基建服务
2. **UI 组件库** - 边缘友好的组件系统
3. **部分业务功能层** - 核心认证和文件功能

### 进行中 🔄
1. **修复 features 包的编译错误和架构**
2. **配置 Biome 和 Turborepo 工具链**

### 下一步行动 🎯
1. 重构 features 包为纯函数 Service 模式
2. 创建 Remix Edge 应用 (apps/web)
3. 创建独立 Worker 应用 (apps/worker)
4. 集成 Biome 代码质量检查
5. 配置 Wrangler 部署流程

---

## Cloudflare 生态优势

### 性能优势
- **边缘计算**: 全球 300+ 节点，延迟 < 50ms
- **零冷启动**: Workers 无服务器架构
- **HTTP/3**: 自动优化网络协议

### 成本优势
- **免费额度**: 100,000 请求/天
- **按需付费**: 只为实际使用付费
- **简化运维**: 无需服务器管理

### 开发体验
- **统一生态**: Workers + Pages + R2 + D1
- **本地开发**: wrangler dev 完全模拟生产环境
- **即时部署**: Git push 触发自动部署

---

## 成功标准

### 架构完整性
- [ ] 三层依赖关系严格执行
- [ ] Biome 规则零违规
- [ ] Turborepo 缓存命中率 > 80%

### Cloudflare 集成
- [ ] 所有服务运行在 Cloudflare 生态
- [ ] 边缘性能优化到位
- [ ] 部署流程自动化

### 开发体验
- [ ] 本地开发环境与生产一致
- [ ] 增量构建时间 < 30s
- [ ] 代码质量检查自动化
