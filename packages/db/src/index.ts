import { neon } from "@neondatabase/serverless";
import type { Env } from "@repo/config";
import { drizzle } from "drizzle-orm/neon-http";

export function createDatabase(databaseUrl: string) {
  const sql = neon(databaseUrl);
  return drizzle(sql);
}

export function createDatabaseFromEnv(env: Pick<Env, "DATABASE_URL">) {
  if (!env.DATABASE_URL) {
    throw new Error("DATABASE_URL is required");
  }

  return createDatabase(env.DATABASE_URL);
}

export * from "./schema/index.js";
export type { InferSelectModel, InferInsertModel } from "drizzle-orm";
