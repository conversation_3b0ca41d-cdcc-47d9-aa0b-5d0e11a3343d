# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a monorepo for a Remix Cloudflare Neon starter built with Turborepo. The project is currently in transition from Next.js to Remix, with a layered architecture designed for scalability and modularity.

## Development Commands

### Build System
- `pnpm build` - Build all apps and packages
- `pnpm dev` - Start development servers for all apps
- `pnpm lint` - Run ESLint across all packages
- `pnpm check-types` - Run TypeScript type checking
- `pnpm format` - Format code with Prettier

### Filtered Commands (Turbo)
- `pnpm turbo build --filter=web` - Build only the web app
- `pnpm turbo dev --filter=web` - Start only the web app in development
- `pnpm turbo build --filter=docs` - Build only the docs app
- `pnpm turbo dev --filter=docs` - Start only the docs app

### Individual App Commands
- **Web app**: `cd apps/web && pnpm dev` (runs on port 3000)
- **Docs app**: `cd apps/docs && pnpm dev` (runs on port 3001)

## Architecture

### Monorepo Structure
```
├── apps/
│   ├── web/        # Main Remix web application (migrating from Next.js)
│   └── docs/       # Next.js documentation site
└── packages/
    ├── shared/     # Common utilities, types, and constants
    ├── ui-kit/     # React component library with Radix UI
    ├── storage/    # Cloudflare R2 and S3 providers
    ├── ai/         # AI-related functionality
    ├── billing/    # Payment and subscription logic
    ├── analytics/  # Analytics and tracking
    └── [others]/   # Additional feature packages
```

### Key Technologies
- **Runtime**: Cloudflare Workers
- **Frontend**: Remix (transitioning from Next.js)
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Auth**: BetterAuth
- **Styling**: Tailwind CSS
- **Components**: Radix UI
- **Build**: Turborepo with pnpm workspaces

### Package Exports
- `@repo/shared` exports: `./types`, `./constants`, `./utils`
- `@repo/ui-kit` exports: `.`, `./components/*`, `./primitives/*`, `./styles/*`, `./utils`

## Development Workflow

### Adding New Components to UI Kit
Use the component generator: `cd packages/ui-kit && pnpm generate:component`

### Package Dependencies
- Use `workspace:*` for internal package dependencies
- All packages use TypeScript 5.8.2 and React 19.1.0
- ESLint configured with `--max-warnings 0` for strict linting

### Build Pipeline
- Turbo handles build orchestration with dependency awareness
- TypeScript compilation runs in parallel across packages
- Outputs cached for performance (`dist/` directories)

## Important Notes

### Migration Status
The project is actively migrating from Next.js to Remix. The `apps/web` directory still contains Next.js configuration but is being transitioned to Remix architecture.

### Package Manager
This project uses `pnpm` as the package manager. Always use `pnpm` commands, not `npm` or `yarn`.

### Monorepo Conventions
- All internal packages are prefixed with `@repo/`
- Shared configurations are in `packages/eslint-config` and `packages/typescript-config`
- Use Turbo filters for targeted builds and development