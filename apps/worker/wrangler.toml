name = "api-worker"
main = "./dist/index.js"
compatibility_date = "2025-06-26"
compatibility_flags = ["nodejs_compat"]

[vars]
NODE_ENV = "development"

# R2 bucket binding for file uploads
[[r2_buckets]]
binding = "ASSETS"
bucket_name = "app-assets"

# KV namespace for caching
[[kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"

# D1 database binding (if needed)
# [[d1_databases]]
# binding = "DB"
# database_name = "app-database"
# database_id = "your-database-id"

# Environment-specific overrides
[env.production]
vars = { NODE_ENV = "production" }

[env.development]
vars = { NODE_ENV = "development" }