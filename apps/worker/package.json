{"name": "worker", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "wrangler dev --port 8787", "build": "tsc", "deploy": "wrangler deploy", "lint": "biome check .", "lint:fix": "biome check --write .", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/shared": "workspace:*"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@cloudflare/workers-types": "^4.20250109.0", "typescript": "5.8.2", "wrangler": "^3.114.10"}}