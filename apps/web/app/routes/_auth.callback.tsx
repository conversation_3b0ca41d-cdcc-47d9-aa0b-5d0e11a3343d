import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { getAuth } from "~/lib/auth.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  const auth = getAuth(context);
  const url = new URL(request.url);

  try {
    const result = await auth.api.callback({
      query: Object.fromEntries(url.searchParams.entries()),
      headers: request.headers,
    });

    if (result.error) {
      // Redirect to login with error
      return redirect(`/login?error=${encodeURIComponent(result.error.message)}`);
    }

    // Set session cookie and redirect to dashboard
    return redirect("/dashboard", {
      headers: {
        "Set-Cookie": result.data?.cookie || "",
      },
    });
  } catch (error) {
    console.error("OAuth callback error:", error);
    return redirect("/login?error=oauth_callback_failed");
  }
}
