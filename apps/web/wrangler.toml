name = "remix-edge-app"
compatibility_date = "2025-06-26"
pages_build_output_dir = "./build/client"

[[env.development.vars]]
NODE_ENV = "development"

[[env.production.vars]]
NODE_ENV = "production"

# Cloudflare Pages Functions configuration
[env.development]
compatibility_flags = ["nodejs_compat"]

[env.production]
compatibility_flags = ["nodejs_compat"]

# R2 bucket binding (uncomment when needed)
# [[r2_buckets]]
# binding = "R2"
# bucket_name = "app-assets"

# KV namespace binding (uncomment when needed)
# [[kv_namespaces]]
# binding = "KV"
# id = "your-kv-namespace-id"

# D1 database binding (uncomment when needed)
# [[d1_databases]]
# binding = "DB"
# database_name = "app-database"
# database_id = "your-database-id"