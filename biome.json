{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["**/node_modules/**", "**/dist/**", "**/.next/**", "**/coverage/**", "**/.turbo/**"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off"}, "correctness": {"noUnusedVariables": "error", "noUnusedImports": "error"}, "nursery": {"noRestrictedImports": {"level": "error", "options": {"paths": {"packages/infra": "Use specific infra imports instead of root package", "packages/ui-kit": "Use specific ui-kit component imports instead of root package"}}}}}}, "javascript": {"formatter": {"quoteStyle": "double", "trailingCommas": "es5", "semicolons": "always"}}, "json": {"formatter": {"trailingCommas": "none"}}}